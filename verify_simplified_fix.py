#!/usr/bin/env python3
"""验证极简时间戳字段修复"""

import time

def test_simplified_timestamp_logic():
    """测试极简时间戳字段逻辑"""
    
    print("🔧 验证极简时间戳字段修复")
    print("=" * 50)
    
    current_time = int(time.time() * 1000)
    
    # 测试Gate.io：只用t字段
    gate_data = {
        "t": current_time,  # 最佳字段（3-4ms延迟）
        "time_ms": current_time - 30000,  # 过期字段（被忽略）
        "s": "BTC_USDT"
    }
    
    # 测试Bybit：只用ts字段
    bybit_data = {
        "ts": current_time,  # 最佳字段（100-200ms延迟）
        "cts": current_time - 5000,  # 其他字段（被忽略）
        "s": "BTCUSDT"
    }
    
    # 测试OKX：只用ts字段
    okx_data = {
        "ts": str(current_time),  # 最佳字段（28-37ms延迟）
        "asks": [["50000", "1.0"]],
        "bids": [["49999", "1.0"]]
    }
    
    test_cases = [
        ("Gate.io", gate_data, "gate"),
        ("Bybit", bybit_data, "bybit"),
        ("OKX", okx_data, "okx")
    ]
    
    for exchange_name, test_data, exchange_code in test_cases:
        print(f"\n📋 测试{exchange_name}")
        print(f"输入数据: {test_data}")
        
        # 模拟极简逻辑
        extracted_timestamp = None
        extraction_source = None
        
        if exchange_code == "gate":
            if 't' in test_data:
                t_value = test_data['t']
                if isinstance(t_value, (int, float)):
                    extracted_timestamp = int(t_value)
                else:
                    extracted_timestamp = int(float(t_value))
                extraction_source = "gate_t_field"
        
        elif exchange_code == "bybit":
            if 'ts' in test_data:
                extracted_timestamp = int(test_data['ts'])
                extraction_source = "bybit_ts_field"
        
        elif exchange_code == "okx":
            if 'ts' in test_data:
                ts_value = test_data['ts']
                try:
                    if isinstance(ts_value, str):
                        extracted_timestamp = int(ts_value)
                    elif isinstance(ts_value, (int, float)):
                        extracted_timestamp = int(ts_value) if ts_value >= 1e12 else int(ts_value * 1000)
                    else:
                        extracted_timestamp = int(float(ts_value))
                except (ValueError, TypeError) as e:
                    print(f"OKX ts字段转换失败: {ts_value}, 错误: {e}")
                    extracted_timestamp = None
                extraction_source = "okx_ts_field"
        
        if extracted_timestamp:
            timestamp_age = abs(extracted_timestamp - current_time)
            print(f"✅ 提取时间戳: {extracted_timestamp}")
            print(f"📍 提取来源: {extraction_source}")
            print(f"⏱️  时间戳年龄: {timestamp_age}ms")
            
            if timestamp_age <= 1000:
                print("🟢 数据新鲜度: 通过")
            else:
                print("🔴 数据新鲜度: 失败")
        else:
            print("❌ 未提取到时间戳")
    
    print("\n" + "=" * 50)
    print("🎯 极简修复总结:")
    print("✅ Gate.io：只用t字段（3-4ms延迟）")
    print("✅ Bybit：只用ts字段（100-200ms延迟）")
    print("✅ OKX：只用ts字段（28-37ms延迟）")
    print("🚫 移除所有冗余字段和复杂逻辑")

if __name__ == "__main__":
    test_simplified_timestamp_logic()
