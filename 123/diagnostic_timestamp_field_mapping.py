# -*- coding: utf-8 -*-
"""
🔥 时间戳字段映射诊断脚本 - 精确定位问题根源
基于用户日志分析，专门诊断Gate.io数据新鲜度检查失败问题
"""

import asyncio
import json
import time
import logging
from typing import Dict, Any, Optional
from websocket.unified_timestamp_processor import get_timestamp_processor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class TimestampFieldDiagnostic:
    """时间戳字段映射诊断器 - 专注于Gate.io问题"""
    
    def __init__(self):
        # 🔥 基于实际日志的测试数据
        self.real_log_data = {
            "gate_failing_cases": [
                # 从日志中提取的实际失败案例
                {"t": 1754124018752, "s": "BTC_USDT", "asks": [], "bids": []},  # 29265ms过期
                {"t": 1754124032091, "s": "BTC_USDT", "asks": [], "bids": []},  # 15928ms过期
                {"t": 1754124019164, "s": "BTC_USDT", "asks": [], "bids": []},  # 30369ms过期
                {"t": 1754124021765, "s": "BTC_USDT", "asks": [], "bids": []},  # 32472ms过期
            ],
            "bybit_working_cases": [
                # Bybit正常工作的数据格式
                {"ts": int(time.time() * 1000), "data": {"s": "BTCUSDT", "b": [], "a": []}},
                {"cts": int(time.time() * 1000), "data": {"s": "BTCUSDT", "b": [], "a": []}},
            ],
            "okx_cases": [
                # OKX的各种情况
                {"data": [{"ts": str(int(time.time() * 1000)), "asks": [], "bids": []}]},  # API响应
                {"data": [{"asks": [], "bids": [], "checksum": 123}]},  # WebSocket订单簿（无ts）
            ]
        }
    
    def analyze_gate_timestamp_extraction(self):
        """分析Gate.io时间戳提取问题"""
        logger.info("🔍 分析Gate.io时间戳提取问题...")
        
        processor = get_timestamp_processor("gate")
        current_time = int(time.time() * 1000)
        
        logger.info(f"📅 当前时间戳: {current_time}")
        
        for i, test_data in enumerate(self.real_log_data["gate_failing_cases"], 1):
            logger.info(f"\n📋 Gate.io失败案例 {i}:")
            logger.info(f"   原始't'字段值: {test_data['t']}")
            
            # 计算实际时间差
            actual_age = current_time - test_data['t']
            logger.info(f"   实际时间差: {actual_age:.1f}ms")
            
            # 测试时间戳提取
            extracted = processor._extract_server_timestamp_for_monitoring(test_data)
            logger.info(f"   提取结果: {extracted}")
            
            # 测试统一时间戳获取
            unified = processor.get_synced_timestamp(test_data)
            unified_age = abs(current_time - unified)
            logger.info(f"   统一时间戳: {unified}")
            logger.info(f"   统一时间差: {unified_age:.1f}ms")
            
            # 分析问题
            if extracted is None:
                logger.info("   ✅ 正确拒绝过期时间戳")
            else:
                logger.warning(f"   ⚠️ 错误接受过期时间戳: {extracted}")
            
            if unified_age < 100:
                logger.info("   ✅ 正确回退到当前时间")
            else:
                logger.warning(f"   ❌ 回退失败，时间差: {unified_age:.1f}ms")
    
    def compare_exchange_behaviors(self):
        """对比三交易所行为差异"""
        logger.info("\n🔍 对比三交易所时间戳处理行为...")
        
        current_time = int(time.time() * 1000)
        
        # 测试Bybit（正常工作）
        logger.info("\n🏪 Bybit行为分析:")
        bybit_processor = get_timestamp_processor("bybit")
        
        for i, test_data in enumerate(self.real_log_data["bybit_working_cases"], 1):
            logger.info(f"  📋 Bybit案例 {i}:")
            extracted = bybit_processor._extract_server_timestamp_for_monitoring(test_data)
            unified = bybit_processor.get_synced_timestamp(test_data)
            
            if extracted:
                age = abs(current_time - extracted)
                logger.info(f"     提取成功: {extracted}, 时间差: {age:.1f}ms")
            else:
                logger.info(f"     提取失败: {extracted}")
            
            unified_age = abs(current_time - unified)
            logger.info(f"     统一时间戳: {unified}, 时间差: {unified_age:.1f}ms")
        
        # 测试OKX
        logger.info("\n🏪 OKX行为分析:")
        okx_processor = get_timestamp_processor("okx")
        
        for i, test_data in enumerate(self.real_log_data["okx_cases"], 1):
            logger.info(f"  📋 OKX案例 {i}:")
            extracted = okx_processor._extract_server_timestamp_for_monitoring(test_data)
            unified = okx_processor.get_synced_timestamp(test_data)
            
            if extracted:
                age = abs(current_time - extracted)
                logger.info(f"     提取成功: {extracted}, 时间差: {age:.1f}ms")
            else:
                logger.info(f"     提取失败: {extracted}")
            
            unified_age = abs(current_time - unified)
            logger.info(f"     统一时间戳: {unified}, 时间差: {unified_age:.1f}ms")
    
    def test_field_mapping_correctness(self):
        """测试字段映射正确性"""
        logger.info("\n🔍 测试字段映射正确性...")
        
        # 测试Gate.io字段优先级：time_ms > t > create_time_ms > timestamp
        gate_test_cases = [
            {"time_ms": 1000, "t": 2000, "create_time_ms": 3000, "timestamp": 4000},
            {"t": 2000, "create_time_ms": 3000, "timestamp": 4000},
            {"create_time_ms": 3000, "timestamp": 4000},
            {"timestamp": 4000},
        ]
        
        expected_results = [1000, 2000, 3000, 4000]
        
        processor = get_timestamp_processor("gate")
        
        for i, (test_data, expected) in enumerate(zip(gate_test_cases, expected_results), 1):
            logger.info(f"\n📋 Gate.io字段优先级测试 {i}:")
            logger.info(f"   测试数据: {test_data}")
            logger.info(f"   期望结果: {expected}")
            
            # 临时修改时间戳为当前时间附近，避免新鲜度检查失败
            current_time = int(time.time() * 1000)
            adjusted_data = {}
            for key, value in test_data.items():
                adjusted_data[key] = current_time - (value * 10)  # 调整为最近的时间戳
            
            extracted = processor._extract_server_timestamp_for_monitoring(adjusted_data)
            logger.info(f"   实际结果: {extracted}")
            
            if extracted:
                # 检查是否选择了正确的字段
                expected_adjusted = current_time - (expected * 10)
                if extracted == expected_adjusted:
                    logger.info("   ✅ 字段优先级正确")
                else:
                    logger.warning(f"   ⚠️ 字段优先级错误，期望: {expected_adjusted}")
            else:
                logger.warning("   ❌ 提取失败")
    
    def diagnose_silent_disconnect_correlation(self):
        """诊断静默断开与时间戳问题的关联"""
        logger.info("\n🔍 诊断静默断开与时间戳问题的关联...")
        
        # 模拟静默断开场景：数据流阻塞30秒以上
        old_timestamp = int(time.time() * 1000) - 32000  # 32秒前
        
        gate_blocked_data = {
            "t": old_timestamp,
            "s": "BTC_USDT",
            "asks": [["16611.00", "0.029"]],
            "bids": [["16493.50", "0.006"]]
        }
        
        logger.info(f"📋 模拟静默断开场景:")
        logger.info(f"   阻塞时间戳: {old_timestamp}")
        logger.info(f"   阻塞时长: 32000ms")
        
        processor = get_timestamp_processor("gate")
        
        # 测试是否触发静默断开检测
        extracted = processor._extract_server_timestamp_for_monitoring(gate_blocked_data)
        
        if extracted is None:
            logger.info("   ✅ 正确拒绝阻塞数据")
        else:
            logger.warning(f"   ⚠️ 错误接受阻塞数据: {extracted}")
        
        # 检查是否应该触发静默断开警告
        current_time = int(time.time() * 1000)
        time_diff = current_time - old_timestamp
        
        if time_diff > 30000:
            logger.info(f"   ✅ 应该触发静默断开警告: {time_diff/1000:.1f}秒")
        else:
            logger.info(f"   ℹ️ 未达到静默断开阈值: {time_diff/1000:.1f}秒")
    
    def run_targeted_diagnostic(self):
        """运行针对性诊断"""
        logger.info("🚀 开始针对性时间戳问题诊断...")
        logger.info("🎯 重点：为什么Gate.io报错而Bybit不报错")
        
        self.analyze_gate_timestamp_extraction()
        self.compare_exchange_behaviors()
        self.test_field_mapping_correctness()
        self.diagnose_silent_disconnect_correlation()
        
        logger.info("\n📊 诊断总结:")
        logger.info("1. Gate.io使用't'字段提取时间戳")
        logger.info("2. 当't'字段时间戳过期(>1000ms)时，系统拒绝并回退到当前时间")
        logger.info("3. Bybit使用'ts'/'cts'字段，数据更新及时，很少过期")
        logger.info("4. OKX WebSocket订单簿数据本身就没有服务器时间戳，直接使用当前时间")
        logger.info("5. Gate.io的数据新鲜度检查失败表明WebSocket数据流可能存在延迟或阻塞")
        
        logger.info("\n✅ 诊断完成！")

async def main():
    """主函数"""
    diagnostic = TimestampFieldDiagnostic()
    diagnostic.run_targeted_diagnostic()

if __name__ == "__main__":
    asyncio.run(main())
