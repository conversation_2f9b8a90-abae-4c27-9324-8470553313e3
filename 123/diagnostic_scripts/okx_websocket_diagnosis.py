#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OKX WebSocket数据流阻塞和时间戳不同步精确诊断脚本

🎯 目标问题：
1. OKX WebSocket数据流长时间阻塞（30.39秒持续数据流停止）
2. 大量时间戳不同步错误（高达25436ms时间差，超过800ms阈值）

🔬 诊断策略：
- 实时监控OKX WebSocket数据流
- 精确测量时间戳同步状态
- 模拟阻塞场景并记录详细日志
- 对比三交易所时间戳处理一致性
"""

import asyncio
import json
import time
import logging
import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import traceback

# 添加项目路径
import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入需要的模块
try:
    from websocket.okx_ws import OKXWebSocketClient
    from websocket.unified_timestamp_processor import get_timestamp_processor, ensure_milliseconds_timestamp
    from utils.logger import get_logger
    print("✅ 成功导入核心模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/root/myproject/123/67D okx 还是有问题/123/logs/okx_diagnosis.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class OKXWebSocketDiagnostics:
    """OKX WebSocket诊断器"""
    
    def __init__(self):
        self.logger = logger
        self.diagnosis_results = {
            "timestamp_sync_issues": [],
            "data_flow_blocks": [],
            "connection_issues": [],
            "performance_metrics": {}
        }
        
        # 监控状态
        self.last_data_time = 0
        self.data_count = 0
        self.timestamp_errors = 0
        self.max_time_gap = 0
        self.start_time = time.time()
        
        # 时间戳处理器
        self.timestamp_processor = get_timestamp_processor("okx")
        
        # 测试交易对
        self.test_symbols = ["BTC-USDT", "ETH-USDT"]
        
    async def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        self.logger.info("🔬 开始OKX WebSocket综合诊断")
        
        try:
            # 第一步：时间戳同步诊断
            await self._diagnose_timestamp_sync()
            
            # 第二步：WebSocket连接诊断
            await self._diagnose_websocket_connection()
            
            # 第三步：数据流阻塞诊断
            await self._diagnose_data_flow_blocking()
            
            # 第四步：跨交易所时间戳一致性诊断
            await self._diagnose_cross_exchange_consistency()
            
            # 生成诊断报告
            self._generate_diagnosis_report()
            
        except Exception as e:
            self.logger.error(f"❌ 诊断过程异常: {e}")
            traceback.print_exc()
    
    async def _diagnose_timestamp_sync(self):
        """诊断时间戳同步问题"""
        self.logger.info("🕐 开始时间戳同步诊断")
        
        try:
            # 强制同步时间
            sync_success = await self.timestamp_processor.sync_time(force=True)
            status = self.timestamp_processor.get_sync_status()
            
            self.logger.info(f"时间同步状态: {json.dumps(status, indent=2)}")
            
            if not sync_success:
                self.diagnosis_results["timestamp_sync_issues"].append({
                    "issue": "时间同步失败",
                    "details": status,
                    "timestamp": time.time()
                })
            
            # 测试时间戳生成
            for i in range(10):
                mock_data = {"ts": str(int(time.time() * 1000))}
                generated_timestamp = self.timestamp_processor.get_synced_timestamp(mock_data)
                current_time = int(time.time() * 1000)
                time_diff = abs(generated_timestamp - current_time)
                
                self.logger.info(f"时间戳测试 {i+1}: 生成={generated_timestamp}, 当前={current_time}, 差异={time_diff}ms")
                
                if time_diff > 800:  # 超过阈值
                    self.diagnosis_results["timestamp_sync_issues"].append({
                        "issue": "时间戳差异超过阈值",
                        "generated": generated_timestamp,
                        "current": current_time,
                        "diff_ms": time_diff,
                        "threshold": 800
                    })
                
                await asyncio.sleep(0.1)
            
        except Exception as e:
            self.logger.error(f"❌ 时间戳同步诊断失败: {e}")
            self.diagnosis_results["timestamp_sync_issues"].append({
                "issue": "诊断异常",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def _diagnose_websocket_connection(self):
        """诊断WebSocket连接问题"""
        self.logger.info("🔌 开始WebSocket连接诊断")
        
        try:
            # 创建OKX WebSocket客户端
            client = OKXWebSocketClient("spot")
            client.set_symbols(self.test_symbols)
            
            # 设置数据处理回调
            client.set_data_handler(self._websocket_data_handler)
            
            # 连接测试
            connection_start = time.time()
            
            # 模拟连接并监控
            connection_task = asyncio.create_task(client.run())
            monitoring_task = asyncio.create_task(self._monitor_connection(client))
            
            # 运行诊断（60秒）
            try:
                await asyncio.wait_for(
                    asyncio.gather(connection_task, monitoring_task), 
                    timeout=60
                )
            except asyncio.TimeoutError:
                self.logger.info("✅ 连接诊断超时完成（正常）")
            
            # 清理
            connection_task.cancel()
            monitoring_task.cancel()
            
            try:
                await client.close()
            except:
                pass
                
        except Exception as e:
            self.logger.error(f"❌ WebSocket连接诊断失败: {e}")
            self.diagnosis_results["connection_issues"].append({
                "issue": "连接诊断异常",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    
    async def _websocket_data_handler(self, data: Dict[str, Any]):
        """WebSocket数据处理器"""
        try:
            current_time = time.time()
            self.data_count += 1
            
            # 检查数据时间戳
            if 'ts' in data:
                data_timestamp = int(data['ts'])
                normalized_timestamp = ensure_milliseconds_timestamp(data_timestamp)
                current_timestamp = int(current_time * 1000)
                time_diff = abs(normalized_timestamp - current_timestamp)
                
                # 记录最大时间差
                if time_diff > self.max_time_gap:
                    self.max_time_gap = time_diff
                
                # 检查时间戳同步问题
                if time_diff > 800:  # 超过阈值
                    self.timestamp_errors += 1
                    self.logger.warning(f"⚠️ 时间戳不同步: 数据时间戳={normalized_timestamp}, 当前时间={current_timestamp}, 差异={time_diff}ms")
                    
                    self.diagnosis_results["timestamp_sync_issues"].append({
                        "issue": "实时数据时间戳不同步", 
                        "data_timestamp": normalized_timestamp,
                        "current_timestamp": current_timestamp,
                        "diff_ms": time_diff,
                        "data_sample": data
                    })
            
            # 检查数据流阻塞
            if self.last_data_time > 0:
                gap = current_time - self.last_data_time
                if gap > 5:  # 5秒无数据认为阻塞
                    self.logger.warning(f"🚨 检测到数据流阻塞: {gap:.2f}秒无数据")
                    self.diagnosis_results["data_flow_blocks"].append({
                        "gap_seconds": gap,
                        "last_data_time": self.last_data_time,
                        "current_time": current_time,
                        "data_count_before": self.data_count - 1
                    })
            
            self.last_data_time = current_time
            
            # 每100条数据记录一次状态
            if self.data_count % 100 == 0:
                self.logger.info(f"📊 数据统计: 接收={self.data_count}, 时间戳错误={self.timestamp_errors}, 最大时间差={self.max_time_gap}ms")
            
        except Exception as e:
            self.logger.error(f"❌ 数据处理异常: {e}")
    
    async def _monitor_connection(self, client):
        """监控连接状态"""
        while True:
            try:
                # 检查连接状态
                if hasattr(client, 'websocket') and client.websocket:
                    if client.websocket.closed:
                        self.logger.warning("🔴 WebSocket连接已关闭")
                        self.diagnosis_results["connection_issues"].append({
                            "issue": "连接关闭",
                            "timestamp": time.time()
                        })
                
                # 检查数据流静默
                current_time = time.time()
                if self.last_data_time > 0:
                    silence_duration = current_time - self.last_data_time
                    if silence_duration > 30:  # 30秒无数据
                        self.logger.error(f"🚨 严重数据流阻塞: {silence_duration:.2f}秒无数据")
                        self.diagnosis_results["data_flow_blocks"].append({
                            "severe_block": True,
                            "silence_duration": silence_duration,
                            "last_data_time": self.last_data_time,
                            "current_time": current_time
                        })
                
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ 连接监控异常: {e}")
                await asyncio.sleep(1)
    
    async def _diagnose_data_flow_blocking(self):
        """诊断数据流阻塞问题"""
        self.logger.info("🚫 开始数据流阻塞诊断")
        
        try:
            # 模拟长时间运行并监控数据流
            # 这里可以添加特定的阻塞场景模拟
            
            # 检查是否存在周期性阻塞
            blocking_patterns = []
            
            for block in self.diagnosis_results["data_flow_blocks"]:
                if block.get("gap_seconds", 0) > 10:
                    blocking_patterns.append(block)
            
            if blocking_patterns:
                self.logger.warning(f"🚨 发现 {len(blocking_patterns)} 个数据流阻塞模式")
                
            # 分析阻塞原因
            self._analyze_blocking_causes()
            
        except Exception as e:
            self.logger.error(f"❌ 数据流阻塞诊断失败: {e}")
    
    def _analyze_blocking_causes(self):
        """分析阻塞原因"""
        self.logger.info("🔍 分析数据流阻塞原因")
        
        causes = []
        
        # 分析时间戳错误模式
        if self.timestamp_errors > 0:
            causes.append({
                "cause": "时间戳不同步导致数据被丢弃",
                "evidence": f"发现 {self.timestamp_errors} 个时间戳错误",
                "severity": "HIGH" if self.timestamp_errors > 10 else "MEDIUM"
            })
        
        # 分析连接问题
        connection_issues = len(self.diagnosis_results["connection_issues"])
        if connection_issues > 0:
            causes.append({
                "cause": "WebSocket连接不稳定",
                "evidence": f"发现 {connection_issues} 个连接问题",
                "severity": "HIGH"
            })
        
        # 分析数据流中断
        blocks = self.diagnosis_results["data_flow_blocks"]
        severe_blocks = [b for b in blocks if b.get("severe_block")]
        if severe_blocks:
            causes.append({
                "cause": "严重数据流中断",
                "evidence": f"发现 {len(severe_blocks)} 个严重阻塞（>30秒）",
                "severity": "CRITICAL"
            })
        
        self.diagnosis_results["blocking_analysis"] = causes
        
        for cause in causes:
            self.logger.warning(f"🔍 阻塞原因 [{cause['severity']}]: {cause['cause']} - {cause['evidence']}")
    
    async def _diagnose_cross_exchange_consistency(self):
        """诊断跨交易所时间戳一致性"""
        self.logger.info("🔄 开始跨交易所时间戳一致性诊断")
        
        try:
            # 获取三个交易所的时间戳处理器
            processors = {
                "gate": get_timestamp_processor("gate"),
                "bybit": get_timestamp_processor("bybit"), 
                "okx": get_timestamp_processor("okx")
            }
            
            # 同步所有交易所时间
            sync_results = {}
            for exchange, processor in processors.items():
                sync_success = await processor.sync_time(force=True)
                sync_results[exchange] = {
                    "success": sync_success,
                    "status": processor.get_sync_status()
                }
                self.logger.info(f"{exchange} 同步结果: {sync_success}")
            
            # 测试时间戳一致性
            mock_data = {"ts": str(int(time.time() * 1000))}
            
            consistency_test = {}
            for exchange, processor in processors.items():
                timestamp = processor.get_synced_timestamp(mock_data)
                consistency_test[exchange] = timestamp
            
            # 计算交易所之间的时间差
            timestamps = list(consistency_test.values())
            max_diff = max(timestamps) - min(timestamps)
            
            self.logger.info(f"交易所时间戳: {consistency_test}")
            self.logger.info(f"最大时间差: {max_diff}ms")
            
            if max_diff > 800:
                self.diagnosis_results["timestamp_sync_issues"].append({
                    "issue": "跨交易所时间戳不一致",
                    "timestamps": consistency_test,
                    "max_diff_ms": max_diff,
                    "threshold": 800
                })
            
            self.diagnosis_results["cross_exchange_sync"] = {
                "sync_results": sync_results,
                "consistency_test": consistency_test,
                "max_diff_ms": max_diff
            }
            
        except Exception as e:
            self.logger.error(f"❌ 跨交易所一致性诊断失败: {e}")
    
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        self.logger.info("📋 生成诊断报告")
        
        duration = time.time() - self.start_time
        
        # 计算性能指标
        self.diagnosis_results["performance_metrics"] = {
            "duration_seconds": duration,
            "total_data_received": self.data_count,
            "data_rate_per_second": self.data_count / duration if duration > 0 else 0,
            "timestamp_error_rate": self.timestamp_errors / self.data_count if self.data_count > 0 else 0,
            "max_time_gap_ms": self.max_time_gap
        }
        
        # 生成总结
        summary = {
            "total_issues": (
                len(self.diagnosis_results["timestamp_sync_issues"]) +
                len(self.diagnosis_results["data_flow_blocks"]) +
                len(self.diagnosis_results["connection_issues"])
            ),
            "critical_issues": len([
                b for b in self.diagnosis_results["data_flow_blocks"] 
                if b.get("severe_block")
            ]),
            "timestamp_sync_health": "GOOD" if self.timestamp_errors == 0 else "POOR",
            "data_flow_health": "GOOD" if len(self.diagnosis_results["data_flow_blocks"]) == 0 else "POOR"
        }
        
        self.diagnosis_results["summary"] = summary
        
        # 保存详细报告
        report_file = f"/root/myproject/123/67D okx 还是有问题/123/diagnostic_results/okx_diagnosis_report_{int(time.time())}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.diagnosis_results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 诊断报告已保存: {report_file}")
        
        # 打印总结
        self.logger.info("=" * 80)
        self.logger.info("🔬 OKX WebSocket 诊断报告总结")
        self.logger.info("=" * 80)
        self.logger.info(f"总问题数: {summary['total_issues']}")
        self.logger.info(f"严重问题数: {summary['critical_issues']}")
        self.logger.info(f"时间戳同步健康: {summary['timestamp_sync_health']}")
        self.logger.info(f"数据流健康: {summary['data_flow_health']}")
        self.logger.info(f"数据接收率: {self.diagnosis_results['performance_metrics']['data_rate_per_second']:.2f} 条/秒")
        self.logger.info(f"时间戳错误率: {self.diagnosis_results['performance_metrics']['timestamp_error_rate']:.2%}")
        self.logger.info(f"最大时间差: {self.max_time_gap}ms")
        self.logger.info("=" * 80)

async def main():
    """主函数"""
    print("🔬 OKX WebSocket数据流阻塞和时间戳不同步精确诊断")
    print("=" * 80)
    
    diagnostics = OKXWebSocketDiagnostics()
    await diagnostics.run_comprehensive_diagnosis()
    
    print("✅ 诊断完成")

if __name__ == "__main__":
    asyncio.run(main())