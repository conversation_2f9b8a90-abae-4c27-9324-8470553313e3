#!/usr/bin/env python3
"""
测试Gate.io时间戳字段优先级修复
验证result.t字段是否被正确优先使用
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from websocket.unified_timestamp_processor import get_synced_timestamp

def test_gate_timestamp_priority():
    """测试Gate.io时间戳字段优先级"""
    
    print("🧪 测试Gate.io时间戳字段优先级修复")
    print("=" * 60)
    
    current_time = int(time.time() * 1000)
    
    # 测试场景1：只有t字段（最新鲜的时间戳）
    test_data_1 = {
        "t": current_time,  # 最新鲜的时间戳
        "s": "BTC_USDT",
        "asks": [["50000", "1.0"]],
        "bids": [["49999", "1.0"]]
    }
    
    # 测试场景2：同时有t和time_ms字段（应该优先使用t）
    test_data_2 = {
        "t": current_time,  # 最新鲜的时间戳
        "time_ms": current_time - 30000,  # 30秒前的过期时间戳
        "s": "BTC_USDT",
        "asks": [["50000", "1.0"]],
        "bids": [["49999", "1.0"]]
    }
    
    # 测试场景3：只有time_ms字段（应该使用time_ms）
    test_data_3 = {
        "time_ms": current_time,
        "s": "BTC_USDT",
        "asks": [["50000", "1.0"]],
        "bids": [["49999", "1.0"]]
    }
    
    # 测试场景4：模拟实际Gate.io WebSocket数据结构
    test_data_4 = {
        "t": current_time,  # result.t字段，最新鲜
        "s": "BTC_USDT",
        "asks": [["50000.1", "1.0"], ["50001.2", "0.5"]],
        "bids": [["49999.9", "1.0"], ["49998.8", "0.5"]]
    }
    
    test_cases = [
        ("场景1: 只有t字段", test_data_1, current_time),
        ("场景2: t和time_ms字段共存", test_data_2, current_time),
        ("场景3: 只有time_ms字段", test_data_3, current_time),
        ("场景4: 实际Gate.io数据结构", test_data_4, current_time)
    ]
    
    for test_name, test_data, expected_timestamp in test_cases:
        print(f"\n📋 {test_name}")
        print(f"输入数据: {test_data}")
        
        try:
            result_timestamp = get_synced_timestamp("gate", test_data)
            timestamp_age = abs(result_timestamp - current_time)
            
            print(f"✅ 提取时间戳: {result_timestamp}")
            print(f"⏱️  时间戳年龄: {timestamp_age}ms")
            
            if test_name == "场景2: t和time_ms字段共存":
                # 验证是否优先使用了t字段而不是time_ms字段
                if result_timestamp == test_data["t"]:
                    print("🎯 ✅ 正确优先使用了t字段（最新鲜）")
                elif result_timestamp == test_data["time_ms"]:
                    print("❌ 错误使用了time_ms字段（过期）")
                else:
                    print("❓ 使用了其他时间戳来源")
            
            # 检查数据新鲜度
            if timestamp_age <= 1000:
                print("🟢 数据新鲜度: 通过 (≤1000ms)")
            else:
                print(f"🔴 数据新鲜度: 失败 ({timestamp_age}ms > 1000ms)")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("\n📊 预期结果:")
    print("- 场景1: 应该成功提取t字段时间戳")
    print("- 场景2: 应该优先使用t字段，忽略time_ms字段")
    print("- 场景3: 应该使用time_ms字段")
    print("- 场景4: 应该成功提取实际数据结构中的t字段")
    print("\n🔧 如果场景2中使用了time_ms而不是t，说明优先级修复未生效")

if __name__ == "__main__":
    test_gate_timestamp_priority()
