{"timestamp_sync_issues": [], "data_flow_blocks": [], "connection_issues": [{"issue": "连接诊断异常", "error": "'OKXWebSocketClient' object has no attribute 'set_data_handler'", "traceback": "Traceback (most recent call last):\n  File \"/root/myproject/123/67D okx 还是有问题/123/diagnostic_scripts/okx_websocket_diagnosis.py\", line 163, in _diagnose_websocket_connection\n    client.set_data_handler(self._websocket_data_handler)\n    ^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'OKXWebSocketClient' object has no attribute 'set_data_handler'\n"}], "performance_metrics": {"duration_seconds": 1.7955193519592285, "total_data_received": 0, "data_rate_per_second": 0.0, "timestamp_error_rate": 0, "max_time_gap_ms": 0}, "blocking_analysis": [{"cause": "WebSocket连接不稳定", "evidence": "发现 1 个连接问题", "severity": "HIGH"}], "cross_exchange_sync": {"sync_results": {"gate": {"success": true, "status": {"exchange": "gate", "time_synced": true, "time_offset_ms": -4, "last_sync_time": 1754117717.9173968, "sync_age_seconds": 0.1589665412902832, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, "bybit": {"success": true, "status": {"exchange": "bybit", "time_synced": true, "time_offset_ms": -38, "last_sync_time": **********.0765252, "sync_age_seconds": 0.13118290901184082, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, "okx": {"success": true, "status": {"exchange": "okx", "time_synced": true, "time_offset_ms": -31, "last_sync_time": **********.2077923, "sync_age_seconds": 0.13884401321411133, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}}, "consistency_test": {"gate": **********346, "bybit": **********346, "okx": **********346}, "max_diff_ms": 0}, "summary": {"total_issues": 1, "critical_issues": 0, "timestamp_sync_health": "GOOD", "data_flow_health": "GOOD"}}