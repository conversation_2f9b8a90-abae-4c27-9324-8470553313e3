2025-08-02 10:37:33 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-02 10:37:33 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-02 10:37:33 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-02 10:37:33 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-02 10:37:33.586 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-02 10:37:33.586 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-02 10:37:33 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-02 10:37:33 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-02 10:37:33 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-02 10:37:34 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-02 10:37:34.029 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:37:34.368 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:37:34.368 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:37:34.692 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:37:34.693 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:37:34 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-02 10:37:34.693 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:37:35.026 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:37:35.026 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:37:35.357 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:37:35.357 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:37:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-02 10:37:35.358 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:37:35.693 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:37:35.693 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:37:36.013 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:37:36.013 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:37:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-02 10:37:36.013 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:37:36.347 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:37:36.347 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:37:36.677 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:37:36.677 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:37:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-02 10:37:36 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-02 10:37:37.002 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 10:37:38.397 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 10:37:39.878 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:37:40.454 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:37:41.030 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:37:41.388 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:37:42.039 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:37:42.624 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:38:02.594 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:02.594 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:03.249 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:03.249 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:03.918 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:03.918 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:04.573 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:04.573 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:05.248 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:05.248 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:05.897 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:05.897 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:48.235 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 10:38:48.544 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 10:38:48.562 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-02 10:38:48.570 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:38:48.617 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:38:51.029 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-02 10:38:51.357 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-02 10:38:51.368 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-02 10:38:51.382 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-02 10:38:51.394 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-02 10:38:52.395 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.729 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.730 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.731 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.731 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-02 10:38:52.814 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:52.815 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:53.148 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:53.148 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:53.149 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:53.149 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 10:38:53.151 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 10:38:53.152 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 10:38:53.152 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 10:38:53 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 10:38:53.153 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:53.153 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:53.153 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 10:38:53.153 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 10:38:53.153 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 10:38:53.153 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 10:38:53.153 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 10:38:53.154 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 10:38:53.154 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 10:38:53.154 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 10:38:53 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 10:38:53.154 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:53.154 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 10:38:53.165 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 10:38:53.166 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 10:38:53.166 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 10:38:53 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 10:38:53.168 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 10:38:53.168 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 10:38:53.168 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 10:38:53.168 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 10:38:53.168 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 10:38:53.169 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 10:38:53.169 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 10:38:53.169 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 10:38:53 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 10:38:53.170 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-02 10:38:53.170 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-02 10:38:53.170 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-02 10:38:53.170 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-02 10:38:53.170 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-02 10:38:53.171 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-02 10:38:53.171 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-02 10:38:53.171 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-02 10:38:53 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-02 10:38:53.172 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:53.173 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:53.479 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:53.479 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:53.480 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:53.480 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:53.480 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:53.480 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:53.482 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:53.482 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:55.228 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:55.228 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:55.560 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:55.560 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:55.567 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:55.567 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:55.572 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:55.572 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:55.573 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:55.573 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:55.597 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-02 10:38:55.597 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-02 10:38:55.891 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:55.891 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:55.901 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:55.901 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:55.902 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:55.902 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:38:55.906 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-02 10:38:55.907 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-02 10:39:04.330 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 10:39:05.841 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 10:39:07.753 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:08.345 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:08.908 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:09.270 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:39:09.852 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:39:10.420 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:39:15.163 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-02 10:39:16.646 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-02 10:39:18.191 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:18.875 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:19.547 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-02 10:39:19.777 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:39:20.357 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-02 10:39:20.974 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
