#!/usr/bin/env python3
"""简单测试Gate.io时间戳修复"""

import time

# 模拟统一时间戳处理器的Gate.io逻辑
def test_gate_timestamp_logic():
    current_time = int(time.time() * 1000)
    
    # 测试数据：同时有t和time_ms字段
    test_data = {
        "t": current_time,  # 最新鲜的时间戳
        "time_ms": current_time - 30000,  # 30秒前的过期时间戳
        "s": "BTC_USDT"
    }
    
    print("🧪 测试Gate.io时间戳字段优先级")
    print(f"当前时间: {current_time}")
    print(f"测试数据: {test_data}")
    
    # 模拟修复后的逻辑：t > time_ms > create_time_ms > timestamp
    extracted_timestamp = None
    extraction_source = None
    
    if 't' in test_data:
        extracted_timestamp = int(test_data['t'])
        extraction_source = "gate_t_field"
        print(f"✅ 使用t字段: {extracted_timestamp}")
    elif 'time_ms' in test_data:
        extracted_timestamp = int(test_data['time_ms'])
        extraction_source = "gate_time_ms_field"
        print(f"⚠️ 使用time_ms字段: {extracted_timestamp}")
    
    timestamp_age = abs(extracted_timestamp - current_time)
    print(f"时间戳年龄: {timestamp_age}ms")
    print(f"提取来源: {extraction_source}")
    
    if timestamp_age <= 1000:
        print("🟢 数据新鲜度: 通过")
    else:
        print("🔴 数据新鲜度: 失败")
    
    return extracted_timestamp == test_data['t']

if __name__ == "__main__":
    success = test_gate_timestamp_logic()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    if success:
        print("✅ 修复生效：优先使用了t字段（最新鲜的时间戳）")
    else:
        print("❌ 修复未生效：仍在使用time_ms字段（过期时间戳）")
