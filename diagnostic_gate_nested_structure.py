#!/usr/bin/env python3
"""
🔍 Gate.io嵌套结构时间戳诊断脚本
专门测试Gate.io WebSocket订单簿数据的嵌套结构问题
"""

import sys
import os
import time
import logging
from typing import Dict, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from websocket.unified_timestamp_processor import get_timestamp_processor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

def test_gate_nested_structure():
    """测试Gate.io嵌套结构时间戳提取问题"""
    logger.info("🔍 开始Gate.io嵌套结构诊断...")
    
    processor = get_timestamp_processor("gate")
    current_time = int(time.time() * 1000)
    
    # 🔥 测试1：当前错误的处理方式 - 顶层t字段
    logger.info("\n📋 测试1: 顶层t字段 (当前错误的数据结构)")
    wrong_data = {
        "time": current_time // 1000,
        "time_ms": current_time,
        "channel": "spot.order_book",
        "event": "update", 
        "t": current_time - 30000,  # 30秒前的过期时间戳
        "s": "BTC_USDT",
        "asks": [["50000.0", "0.1"]],
        "bids": [["49999.0", "0.1"]]
    }
    
    result1 = processor.extract_timestamp(wrong_data)
    age1 = current_time - result1["timestamp"]
    logger.info(f"   提取时间戳: {result1['timestamp']}")
    logger.info(f"   提取来源: {result1['extraction_source']}")
    logger.info(f"   时间戳年龄: {age1}ms ({age1/1000:.1f}秒)")
    logger.info(f"   是否过期: {'是' if age1 > 1000 else '否'}")
    
    # 🔥 测试2：正确的Gate.io数据结构 - result.t字段
    logger.info("\n📋 测试2: result.t字段 (正确的Gate.io WebSocket格式)")
    correct_data = {
        "time": current_time // 1000,
        "time_ms": current_time,
        "channel": "spot.order_book",
        "event": "update",
        "result": {
            "t": current_time - 5,  # 5ms前的新鲜时间戳
            "s": "BTC_USDT",
            "asks": [["50000.0", "0.1"]],
            "bids": [["49999.0", "0.1"]]
        }
    }
    
    result2 = processor.extract_timestamp(correct_data)
    age2 = current_time - result2["timestamp"]
    logger.info(f"   提取时间戳: {result2['timestamp']}")
    logger.info(f"   提取来源: {result2['extraction_source']}")
    logger.info(f"   时间戳年龄: {age2}ms ({age2/1000:.1f}秒)")
    logger.info(f"   是否过期: {'是' if age2 > 1000 else '否'}")
    
    # 🔥 测试3：直接传入result对象
    logger.info("\n📋 测试3: 直接传入result对象")
    result_only = correct_data["result"]
    result3 = processor.extract_timestamp(result_only)
    age3 = current_time - result3["timestamp"]
    logger.info(f"   提取时间戳: {result3['timestamp']}")
    logger.info(f"   提取来源: {result3['extraction_source']}")
    logger.info(f"   时间戳年龄: {age3}ms ({age3/1000:.1f}秒)")
    logger.info(f"   是否过期: {'是' if age3 > 1000 else '否'}")
    
    # 🔥 测试4：模拟实际日志中的失败案例
    logger.info("\n📋 测试4: 模拟实际日志失败案例")
    log_case = {
        "time": 1722588000,
        "time_ms": 1722588000000,
        "channel": "spot.order_book",
        "event": "update",
        "result": {
            "t": 1722571735769,  # 实际日志中的过期时间戳
            "s": "BTC_USDT",
            "asks": [],
            "bids": []
        }
    }
    
    result4 = processor.extract_timestamp(log_case)
    age4 = current_time - result4["timestamp"]
    logger.info(f"   提取时间戳: {result4['timestamp']}")
    logger.info(f"   提取来源: {result4['extraction_source']}")
    logger.info(f"   时间戳年龄: {age4}ms ({age4/1000:.1f}秒)")
    logger.info(f"   是否过期: {'是' if age4 > 1000 else '否'}")
    
    return result1, result2, result3, result4

def test_unified_timestamp_processor_fix():
    """测试统一时间戳处理器是否需要修复"""
    logger.info("\n🔧 测试统一时间戳处理器修复需求...")
    
    processor = get_timestamp_processor("gate")
    current_time = int(time.time() * 1000)
    
    # 模拟真实的Gate.io WebSocket消息
    real_gate_message = {
        "time": current_time // 1000,
        "time_ms": current_time,
        "channel": "spot.order_book",
        "event": "update",
        "result": {
            "t": current_time - 3,  # 3ms前的新鲜时间戳
            "e": "depthUpdate",
            "E": current_time // 1000,
            "s": "BTC_USDT",
            "U": 123456,
            "u": 123457,
            "b": [["49999.0", "0.1"]],
            "a": [["50000.0", "0.1"]]
        }
    }
    
    # 测试当前处理器
    logger.info("📊 当前处理器测试:")
    result = processor.extract_timestamp(real_gate_message)
    age = current_time - result["timestamp"]
    logger.info(f"   提取时间戳: {result['timestamp']}")
    logger.info(f"   提取来源: {result['extraction_source']}")
    logger.info(f"   时间戳年龄: {age}ms")
    logger.info(f"   是否正确: {'是' if age < 100 else '否'}")
    
    # 测试数据新鲜度检查
    logger.info("\n🔍 数据新鲜度检查测试:")
    freshness_result = processor.validate_data_freshness(result["timestamp"])
    logger.info(f"   新鲜度检查结果: {freshness_result}")
    
    return result

def main():
    """主函数"""
    logger.info("🚀 开始Gate.io嵌套结构诊断...")
    
    try:
        # 测试嵌套结构
        results = test_gate_nested_structure()
        
        # 测试处理器修复需求
        processor_result = test_unified_timestamp_processor_fix()
        
        logger.info("\n📋 诊断总结:")
        logger.info("=" * 60)
        
        # 分析结果
        current_time = int(time.time() * 1000)
        
        for i, result in enumerate(results, 1):
            age = current_time - result["timestamp"]
            status = "✅ 正确" if age < 1000 else "❌ 错误"
            logger.info(f"测试{i}: {status} - 年龄{age}ms, 来源{result['extraction_source']}")
        
        # 关键发现
        logger.info("\n🔥 关键发现:")
        if "result" in results[1]["extraction_source"] or results[2]["timestamp"] - results[1]["timestamp"] == 0:
            logger.info("✅ 处理器能够正确处理result.t字段")
        else:
            logger.error("❌ 处理器无法正确处理result.t字段 - 需要修复!")
            
        logger.info("\n🎯 修复建议:")
        logger.info("1. 检查统一时间戳处理器是否正确处理Gate.io的嵌套result结构")
        logger.info("2. 确保时间戳提取优先级: result.t > time_ms > t > timestamp")
        logger.info("3. 验证Gate.io WebSocket处理代码是否传递正确的数据结构")
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}", exc_info=True)
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
